{"firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "storage": {"rules": "storage.rules"}, "functions": {"source": "functions", "runtime": "nodejs18"}, "emulators": {"auth": {"port": 9099}, "firestore": {"port": 8080}, "storage": {"port": 9199}, "functions": {"port": 5001}, "ui": {"enabled": true, "port": 4000}}, "hosting": {"public": "dist", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}], "headers": [{"source": "**/*.@(js|css|jpg|jpeg|gif|png|svg)", "headers": [{"key": "Cache-Control", "value": "max-age=31536000"}]}, {"source": "**", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://apis.google.com https://*.firebaseio.com https://*.googleapis.com https://*.firebaseapp.com https://*.firebase.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: https://res.cloudinary.com https://*.googleusercontent.com https://ui-avatars.com; font-src 'self' data: https://fonts.gstatic.com; connect-src 'self' wss://*.firebaseio.com https://*.googleapis.com https://firestore.googleapis.com https://*.cloudfunctions.net https://api.cloudinary.com https://res.cloudinary.com; frame-src 'self' https://*.firebaseapp.com https://*.google.com"}, {"key": "Permissions-Policy", "value": "accelerometer=(), camera=(), geolocation=(), gyroscope=(), magnetometer=(), microphone=(), payment=(), usb=()"}]}]}}