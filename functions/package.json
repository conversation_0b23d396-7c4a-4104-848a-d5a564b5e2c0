{"name": "functions", "version": "1.0.0", "main": "lib/index.js", "engines": {"node": "18"}, "scripts": {"build": "tsc", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"firebase-admin": "^13.4.0", "firebase-functions": "^6.3.2"}, "devDependencies": {"@types/node": "^22.15.29", "typescript": "^5.8.3"}}