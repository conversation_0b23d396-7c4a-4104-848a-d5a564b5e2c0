import { db } from '../firebase-config';
import {
  doc,
  setDoc,
  getDoc,
  getDocs,
  collection,
  query,
  where,
  orderBy,
  limit,
  Timestamp,
  runTransaction,
  updateDoc
} from 'firebase/firestore';
import {
  UserXP,
  XPTransaction,
  XPSource,
  XPAwardResult,
  LevelCalculationResult,
  LEVEL_TIERS,
  XP_VALUES,
  Achievement,
  UserAchievement,
  AchievementCategory,
  ConditionType
} from '../types/gamification';
import { ServiceResponse } from '../types/services';
import { createNotification } from './notifications';

/**
 * Core XP and Level Management
 */

/**
 * Award XP to a user for a specific action
 */
export const awardXP = async (
  userId: string,
  amount: number,
  source: XPSource,
  sourceId?: string,
  description?: string
): Promise<XPAwardResult> => {
  try {
    const result = await runTransaction(db, async (transaction) => {
      const userXPRef = doc(db, 'userXP', userId);
      const userXPSnap = await transaction.get(userXPRef);
      
      let currentXP: UserXP;
      let isNewUser = false;
      
      if (!userXPSnap.exists()) {
        // Initialize new user XP record
        isNewUser = true;
        currentXP = {
          userId,
          totalXP: 0,
          currentLevel: 1,
          xpToNextLevel: LEVEL_TIERS[0].maxXP,
          lastUpdated: Timestamp.now(),
          createdAt: Timestamp.now()
        };
      } else {
        currentXP = userXPSnap.data() as UserXP;
      }

      // Calculate new XP total
      const newTotalXP = currentXP.totalXP + amount;
      const levelResult = calculateLevel(newTotalXP);
      const leveledUp = levelResult.currentLevel > currentXP.currentLevel;

      // Update user XP record
      const updatedXP: UserXP = {
        ...currentXP,
        totalXP: newTotalXP,
        currentLevel: levelResult.currentLevel,
        xpToNextLevel: levelResult.xpToNextLevel,
        lastUpdated: Timestamp.now()
      };

      transaction.set(userXPRef, updatedXP);

      // Create XP transaction record
      const xpTransactionRef = doc(collection(db, 'xpTransactions'));
      const xpTransaction: XPTransaction = {
        id: xpTransactionRef.id,
        userId,
        amount,
        source,
        sourceId,
        description: description || `${source} reward`,
        createdAt: Timestamp.now()
      };

      transaction.set(xpTransactionRef, xpTransaction);

      return {
        success: true,
        xpAwarded: amount,
        newLevel: leveledUp ? levelResult.currentLevel : undefined,
        leveledUp,
        newAchievements: [] // Will be populated by achievement check
      };
    });

    // Check for new achievements after XP award
    if (result.success) {
      try {
        // Dynamic import to avoid circular dependency
        const { checkAndUnlockAchievements } = await import('./achievements');
        const newAchievements = await checkAndUnlockAchievements(userId);
        result.newAchievements = newAchievements;

        // Send achievement notifications
        for (const achievement of newAchievements) {
          await createAchievementNotification(userId, achievement);
        }
      } catch (achievementError: any) {
        console.warn('Achievement checking failed:', achievementError.message);
        result.newAchievements = [];
      }

      // Send level up notification if applicable
      if (result.leveledUp && result.newLevel) {
        await createLevelUpNotification(userId, result.newLevel);
      }
    }

    return result;
  } catch (error: any) {
    console.error('Error awarding XP:', error);
    return {
      success: false,
      xpAwarded: 0,
      leveledUp: false,
      error: error.message || 'Failed to award XP'
    };
  }
};

/**
 * Calculate user level based on total XP
 */
export const calculateLevel = (totalXP: number): LevelCalculationResult => {
  let currentLevel = 1;
  let currentLevelTier = LEVEL_TIERS[0];

  // Find the appropriate level tier
  for (const tier of LEVEL_TIERS) {
    if (totalXP >= tier.minXP && totalXP <= tier.maxXP) {
      currentLevel = tier.level;
      currentLevelTier = tier;
      break;
    }
  }

  // Calculate XP to next level
  const nextTier = LEVEL_TIERS.find(tier => tier.level === currentLevel + 1);
  const xpToNextLevel = nextTier ? nextTier.minXP - totalXP : 0;
  
  // Calculate progress percentage within current level
  const levelStartXP = currentLevelTier.minXP;
  const levelEndXP = currentLevelTier.maxXP === Infinity ? totalXP + 1000 : currentLevelTier.maxXP;
  const progressPercentage = Math.min(100, ((totalXP - levelStartXP) / (levelEndXP - levelStartXP)) * 100);

  return {
    currentLevel,
    currentLevelTier,
    xpToNextLevel,
    progressPercentage
  };
};

/**
 * Get user's current XP and level information
 */
export const getUserXP = async (userId: string): Promise<ServiceResponse<UserXP>> => {
  try {
    const userXPRef = doc(db, 'userXP', userId);
    const userXPSnap = await getDoc(userXPRef);

    if (!userXPSnap.exists()) {
      // Initialize new user with default values
      const defaultXP: UserXP = {
        userId,
        totalXP: 0,
        currentLevel: 1,
        xpToNextLevel: LEVEL_TIERS[0].maxXP,
        lastUpdated: Timestamp.now(),
        createdAt: Timestamp.now()
      };

      await setDoc(userXPRef, defaultXP);
      return { success: true, data: defaultXP };
    }

    return { success: true, data: userXPSnap.data() as UserXP };
  } catch (error: any) {
    console.error('Error getting user XP:', error);
    return { success: false, error: error.message || 'Failed to get user XP' };
  }
};

/**
 * Get user's XP transaction history
 */
export const getUserXPHistory = async (
  userId: string,
  limitCount: number = 50
): Promise<ServiceResponse<XPTransaction[]>> => {
  try {
    const xpQuery = query(
      collection(db, 'xpTransactions'),
      where('userId', '==', userId),
      orderBy('createdAt', 'desc'),
      limit(limitCount)
    );

    const xpSnap = await getDocs(xpQuery);
    const transactions = xpSnap.docs.map(doc => doc.data() as XPTransaction);

    return { success: true, data: transactions };
  } catch (error: any) {
    console.error('Error getting XP history:', error);
    return { success: false, error: error.message || 'Failed to get XP history' };
  }
};

/**
 * Notification helpers
 */
const createLevelUpNotification = async (userId: string, newLevel: number): Promise<void> => {
  const levelTier = LEVEL_TIERS.find(tier => tier.level === newLevel);
  
  await createNotification({
    recipientId: userId,
    type: 'level_up',
    title: 'Level Up! 🎉',
    message: `Congratulations! You've reached level ${newLevel} - ${levelTier?.title || 'Unknown'}!`,
    data: {
      newLevel,
      levelTitle: levelTier?.title,
      benefits: levelTier?.benefits
    },
    createdAt: Timestamp.now()
  });
};

const createAchievementNotification = async (userId: string, achievement: Achievement): Promise<void> => {
  await createNotification({
    recipientId: userId,
    type: 'achievement_unlocked',
    title: 'Achievement Unlocked! 🏆',
    message: `You've earned the "${achievement.title}" achievement!`,
    data: {
      achievementId: achievement.id,
      achievementTitle: achievement.title,
      xpReward: achievement.xpReward
    },
    createdAt: Timestamp.now()
  });
};

/**
 * Convenience functions for common XP awards
 */
export const awardTradeCompletionXP = async (
  userId: string,
  isQuickResponse: boolean = false,
  isFirstTrade: boolean = false
): Promise<XPAwardResult> => {
  let amount = XP_VALUES.TRADE_COMPLETION;
  let description = 'Trade completion';

  if (isQuickResponse) {
    amount += XP_VALUES.QUICK_RESPONSE_BONUS;
    description += ' (quick response bonus)';
  }

  if (isFirstTrade) {
    amount += XP_VALUES.FIRST_TRADE_BONUS;
    description += ' (first trade bonus)';
  }

  return awardXP(userId, amount, XPSource.TRADE_COMPLETION, undefined, description);
};

export const awardRoleCompletionXP = async (
  userId: string,
  roleId: string,
  isComplex: boolean = false
): Promise<XPAwardResult> => {
  const amount = isComplex ? XP_VALUES.ROLE_COMPLETION_COMPLEX : XP_VALUES.ROLE_COMPLETION_BASE;
  const description = `Role completion${isComplex ? ' (complex role)' : ''}`;

  return awardXP(userId, amount, XPSource.ROLE_COMPLETION, roleId, description);
};

export const awardEvidenceSubmissionXP = async (
  userId: string,
  sourceId: string
): Promise<XPAwardResult> => {
  return awardXP(userId, XP_VALUES.EVIDENCE_SUBMISSION, XPSource.EVIDENCE_SUBMISSION, sourceId, 'Evidence submission');
};


