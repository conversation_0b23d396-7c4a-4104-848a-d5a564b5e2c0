import { db } from '../firebase-config';
import { 
  doc, 
  setDoc, 
  getDoc, 
  getDocs,
  DocumentData, 
  collection, 
  addDoc, 
  Timestamp,
  query,
  where,
  writeBatch,
  orderBy,
  arrayUnion,
  limit as limitQuery,
  QueryDocumentSnapshot,
  startAfter,
  deleteDoc,
  updateDoc
} from 'firebase/firestore';
import { CreateUserProfileData, UpdateUserProfileData, UserRole } from '../firebase-config';
import { ServiceResult } from '../types/ServiceError';
import { EmbeddedEvidence } from '../types/evidence';
import { ChangeRequest, CollaborationRole } from '../types/collaboration';

// Collection names
export const COLLECTIONS = {
  USERS: 'users',
  TRADES: 'trades',
  PROJECTS: 'projects',
  NOTIFICATIONS: 'notifications',
  CONVERSATIONS: 'conversations',
  MESSAGES: 'messages',
  CONNECTIONS: 'connections',
  REVIEWS: 'reviews',
  CHALLENGES: 'challenges',
  COLLABORATIONS: 'collaborations'
} as const;

// Export Timestamp explicitly
export { Timestamp };
export type { ServiceResult };

// Base types
export interface User {
  uid: string;
  id: string;  // Required
  email?: string;
  displayName?: string; // Made optional to match actual data
  profilePicture?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  provider?: string;
  lastSignInTime?: number;
  emailVerified?: boolean;
  role?: UserRole;
  photoURL?: string;
  bestProfilePicture?: string;
  location?: string;
  bio?: string;
  skills?: string;
  interests?: string;
  reputationScore?: number;
}

export interface Trade {
  id?: string;
  creatorId: string;
  _userId?: string;  // Changed from userId to _userId to indicate it's intentionally unused
  title: string;
  description: string;
  offeredSkills: TradeSkill[];
  requestedSkills: TradeSkill[];
  offering: string;
  seeking: string;
  category?: string;
  status: 'open' | 'in-progress' | 'completed' | 'cancelled' | 'pending_confirmation' | 'pending_evidence' | 'disputed';
  createdAt: Timestamp;
  updatedAt: Timestamp;
  creatorName?: string;
  userName?: string;
  creatorEvidence?: EmbeddedEvidence[];
  participantId?: string;
  participantEvidence?: EmbeddedEvidence[];
  completionRequestedBy?: string;
  completionRequestedAt?: Timestamp;
  completionConfirmedAt?: Timestamp;
  creatorCompletionNotes?: string;
  // Auto-completion fields
  autoCompleted?: boolean;
  autoCompletionReason?: string;
  autoCompletionCountdown?: number; // Days remaining until auto-completion
  remindersSent?: number; // Track how many reminders have been sent
  participantCompletionNotes?: string;
  creatorEvidenceSubmittedAt?: Timestamp;
  participantEvidenceSubmittedAt?: Timestamp;
  completionEvidence?: EmbeddedEvidence[];
  completionNotes?: string;
  changeRequests?: ChangeRequest[];
}

export interface Message {
  id?: string;
  senderId: string;
  content: string;
  createdAt: Timestamp;
  read: boolean;
  conversationId: string;
}

// Conversation: Used for general messaging (e.g., trade/project discussions), not real-time chat.
export interface Conversation {
  id?: string;
  participants: { id: string; name: string }[];
  lastMessage?: Message;
  lastMessageTimestamp?: Timestamp;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  unreadCount?: { [userId: string]: number };
}

export interface EvidenceUpdateData {
  evidence: EmbeddedEvidence[];
  notes?: string;
  timestamp: Timestamp;
}

export interface Project {
  id?: string;
  title: string;
  description: string;
  skillsNeeded: string[];
  status: 'open' | 'in-progress' | 'completed' | 'cancelled';
  creatorId: string;
  ownerId: string;
  ownerName: string;
  ownerPhotoURL?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  images?: string[];
  category?: string;
  isRemote?: boolean;
  location?: string;
  timeline?: string;
  compensation?: string;
  collaborators?: string[];
}

export interface ProjectApplication {
  id: string;
  projectId: string;
  applicantId: string;
  message: string;
  status: 'pending' | 'accepted' | 'rejected';
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface Review {
  id?: string;
  reviewerId: string;
  reviewerName: string;
  targetId: string;
  rating: number;
  content: string;
  comment: string;
  tradeId: string;
  createdAt: Timestamp;
}

export interface TradeProposal {
  id: string;
  tradeId: string;
  _userId: string; // Renamed from userId to follow convention for fields not directly read in this module
  userName?: string;
  userPhotoURL?: string;
  userRating?: number;
  offeredSkills: TradeSkill[];
  requestedSkills: TradeSkill[];
  message: string;
  timeframe: string;
  availability: string[];
  status: 'pending' | 'accepted' | 'rejected' | 'withdrawn';
  createdAt: Timestamp;
  updatedAt: Timestamp;
  acceptedAt?: Timestamp;
  rejectedAt?: Timestamp;
}

export interface Challenge {
  id?: string;
  title: string;
  description: string;
  criteria: string[];
  deadline: Timestamp;
  creatorId: string;
  status: 'active' | 'completed' | 'cancelled';
  createdAt: Timestamp;
  updatedAt: Timestamp;
  difficulty?: 'Beginner' | 'Intermediate' | 'Advanced';
  category?: string;
  participants?: string[];
}

export interface Collaboration {
  id?: string;
  title: string;
  description: string;
  roles: CollaborationRole[];
  creatorId: string;
  status: 'open' | 'in-progress' | 'completed' | 'recruiting';
  createdAt: Timestamp;
  updatedAt: Timestamp;
  creatorName?: string;
  creatorPhotoURL?: string;
}

export interface Connection {
  id?: string;
  userId: string;
  connectedUserId: string;
  status: 'pending' | 'accepted' | 'rejected';
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface Notification {
  id?: string;
  userId: string;
  type: 'message' | 'trade_interest' | 'trade_completed' | 'review' | 'project' | 'challenge' | 'system' | 'trade';
  title: string;
  content: string;
  read: boolean;
  createdAt: Timestamp;
  relatedId?: string;
  message?: string;
  data?: {
    tradeId?: string;
    projectId?: string;
    challengeId?: string;
    conversationId?: string;
    url?: string;
  };
}

export interface NotificationData extends Omit<Notification, 'id' | 'createdAt' | 'read'> {}

export type TradeSkill = {
  name: string;
  level: 'beginner' | 'intermediate' | 'expert';
};

export type TradeStatus = 'open' | 'in-progress' | 'completed' | 'cancelled' | 'pending_confirmation' | 'pending_evidence' | 'disputed';

// Create notification in Firestore
export const createNotification = async (data: NotificationData): Promise<ServiceResult<string>> => {
  try {
    const notificationsRef = collection(db, COLLECTIONS.NOTIFICATIONS);
    const docRef = await addDoc(notificationsRef, {
      ...data,
      createdAt: Timestamp.now(),
      read: false,
    });
    return { data: docRef.id, error: null };
  } catch (error: any) {
    console.error('Error creating notification:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to create notification' } };
  }
};

// Mark notification as read
export const markNotificationAsRead = async (notificationId: string): Promise<ServiceResult<void>> => {
  try {
    const notificationRef = doc(db, COLLECTIONS.NOTIFICATIONS, notificationId);
    await updateDoc(notificationRef, { read: true });
    return { data: undefined, error: null };
  } catch (error: any) {
    console.error('Error marking notification as read:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to mark notification as read' } };
  }
};

// Mark all notifications as read for a user
export const markAllNotificationsAsRead = async (userId: string): Promise<ServiceResult<void>> => {
  try {
    const notificationsRef = collection(db, COLLECTIONS.NOTIFICATIONS);
    const userNotificationsQuery = query(notificationsRef, where('userId', '==', userId), where('read', '==', false));
    const userNotificationsSnap = await getDocs(userNotificationsQuery);

    const batch = writeBatch(db);
    userNotificationsSnap.forEach((doc) => {
      batch.update(doc.ref, { read: true });
    });
    await batch.commit();

    return { data: undefined, error: null };
  } catch (error: any) {
    console.error('Error marking all notifications as read:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to mark all notifications as read' } };
  }
};

// Create user profile in Firestore
export const createUserProfile = async (
  uid: string,
  profileData: CreateUserProfileData
): Promise<ServiceResult<void>> => {
  try {
    await setDoc(doc(db, COLLECTIONS.USERS, uid), {
      ...profileData,
      uid,
      id: uid,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
      role: 'user', // Default role
    });
    return { data: undefined, error: null };
  } catch (error: any) {
    console.error('Error creating user profile:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to create user profile' } };
  }
};

// Get user profile from Firestore
export const getUserProfile = async (uid: string): Promise<ServiceResult<User | undefined>> => {
  try {
    const userDoc = await getDoc(doc(db, COLLECTIONS.USERS, uid));
    if (userDoc.exists()) {
      return { data: userDoc.data() as User, error: null };
    } else {
      return { data: undefined, error: null };
    }
  } catch (error: any) {
    console.error('Error getting user profile:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to get user profile' } };
  }
};

// Update user profile in Firestore
// CRUD operations for all collections
export const getAllUsers = async (): Promise<ServiceResult<User[]>> => {
  try {
    const usersCollection = collection(db, COLLECTIONS.USERS);
    const userDocs = await getDocs(usersCollection);

    const users = userDocs.docs.map(doc => {
      const userData = doc.data();

      return {
        id: doc.id,
        uid: userData.uid || doc.id, // Fallback to doc.id if uid is missing
        ...userData,
      } as User;
    });

    return { data: users, error: null };
  } catch (error: any) {
    console.error('Error getting all users:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to get users' } };
  }
};

// Delete user profile from Firestore
export const deleteUser = async (userId: string): Promise<ServiceResult<void>> => {
  try {
    const userRef = doc(db, COLLECTIONS.USERS, userId);
    await deleteDoc(userRef);
    return { data: undefined, error: null };
  } catch (error: any) {
    console.error('Error deleting user:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to delete user' } };
  }
};

export const updateUserRole = async (userId: string, role: UserRole): Promise<ServiceResult<void>> => {
  try {
    const userRef = doc(db, COLLECTIONS.USERS, userId);
    await updateDoc(userRef, { role });
    return { data: undefined, error: null };
  } catch (error: any) {
    console.error('Error updating user role:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to update user role' } };
  }
};

export const createTrade = async (tradeData: Omit<Trade, 'id'>): Promise<ServiceResult<string>> => {
  try {
    const tradesCollection = collection(db, COLLECTIONS.TRADES);
    const docRef = await addDoc(tradesCollection, {
      ...tradeData,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
    });
    return { data: docRef.id, error: null };
  } catch (error: any) {
    console.error('Error creating trade:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to create trade' } };
  }
};

export const getTrade = async (tradeId: string): Promise<ServiceResult<Trade | undefined>> => {
  try {
    const tradeDoc = await getDoc(doc(db, COLLECTIONS.TRADES, tradeId));
    if (tradeDoc.exists()) {
      const trade = tradeDoc.data() as Trade;

      // Add auto-completion countdown if pending confirmation
      if (trade.status === 'pending_confirmation' && trade.completionRequestedAt) {
        trade.autoCompletionCountdown = calculateAutoCompletionCountdown(trade.completionRequestedAt);
      }

      return { data: trade, error: null };
    } else {
      return { data: undefined, error: null };
    }
  } catch (error: any) {
    console.error('Error getting trade:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to get trade' } };
  }
};

export const updateTrade = async (tradeId: string, updates: Partial<Trade>): Promise<ServiceResult<void>> => {
  try {
    const tradeRef = doc(db, COLLECTIONS.TRADES, tradeId);
    await updateDoc(tradeRef, {
      ...updates,
      updatedAt: Timestamp.now(),
    });
    return { data: undefined, error: null };
  } catch (error: any) {
    console.error('Error updating trade:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to update trade' } };
  }
};

export const deleteTrade = async (tradeId: string): Promise<ServiceResult<void>> => {
  try {
    const tradeRef = doc(db, COLLECTIONS.TRADES, tradeId);
    await deleteDoc(tradeRef);
    return { data: undefined, error: null };
  } catch (error: any) {
    console.error('Error deleting trade:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to delete trade' } };
  }
};

export const getAllTrades = async (category?: string, limit?: number): Promise<ServiceResult<Trade[]>> => {
  try {
    let tradesQuery = query(collection(db, COLLECTIONS.TRADES));
    if (category) {
      tradesQuery = query(tradesQuery, where('category', '==', category));
    }
    tradesQuery = query(tradesQuery, orderBy('createdAt', 'desc'));
    if (limit) {
      tradesQuery = query(tradesQuery, limitQuery(limit));
    }
    const tradeDocs = await getDocs(tradesQuery);
    const trades = tradeDocs.docs.map(doc => {
      const trade = { id: doc.id, ...doc.data() as Trade };

      // Add auto-completion countdown if pending confirmation
      if (trade.status === 'pending_confirmation' && trade.completionRequestedAt) {
        trade.autoCompletionCountdown = calculateAutoCompletionCountdown(trade.completionRequestedAt);
      }

      return trade;
    });
    return { data: trades, error: null };
  } catch (error: any) {
    console.error('Error getting all trades:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to get all trades' } };
  }
};

export const getUserTrades = async (userId: string): Promise<ServiceResult<Trade[]>> => {
  try {
    const tradesCollection = collection(db, COLLECTIONS.TRADES);
    const userTradesQuery = query(tradesCollection, where('creatorId', '==', userId), orderBy('createdAt', 'desc'));
    const tradeDocs = await getDocs(userTradesQuery);
    const trades = tradeDocs.docs.map(doc => {
      const trade = { id: doc.id, ...doc.data() as Trade };

      // Add auto-completion countdown if pending confirmation
      if (trade.status === 'pending_confirmation' && trade.completionRequestedAt) {
        trade.autoCompletionCountdown = calculateAutoCompletionCountdown(trade.completionRequestedAt);
      }

      return trade;
    });
    return { data: trades, error: null };
  } catch (error: any) {
    console.error('Error getting user trades:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to get user trades' } };
  }
};

// Project operations
export const createProject = async (projectData: Omit<Project, 'id'>): Promise<ServiceResult<string>> => {
  try {
    const projectsCollection = collection(db, COLLECTIONS.PROJECTS);
    const docRef = await addDoc(projectsCollection, {
      ...projectData,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
    });
    return { data: docRef.id, error: null };
  } catch (error: any) {
    console.error('Error creating project:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to create project' } };
  }
};

export const getProject = async (projectId: string): Promise<ServiceResult<Project | undefined>> => {
  try {
    const projectDoc = await getDoc(doc(db, COLLECTIONS.PROJECTS, projectId));
    if (projectDoc.exists()) {
      return { data: projectDoc.data() as Project, error: null };
    } else {
      return { data: undefined, error: null };
    }
  } catch (error: any) {
    console.error('Error getting project:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to get project' } };
  }
};

export const getAllProjects = async (): Promise<ServiceResult<Project[]>> => {
  try {
    const projectsCollection = collection(db, COLLECTIONS.PROJECTS);
    const projectDocs = await getDocs(projectsCollection);
    const projects = projectDocs.docs.map(doc => ({ id: doc.id, ...doc.data() as Project }));
    return { data: projects, error: null };
  } catch (error: any) {
    console.error('Error getting all projects:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to get all projects' } };
  }
};

export const updateProject = async (projectId: string, updates: Partial<Project>): Promise<ServiceResult<void>> => {
  try {
    const projectRef = doc(db, COLLECTIONS.PROJECTS, projectId);
    await updateDoc(projectRef, {
      ...updates,
      updatedAt: Timestamp.now(),
    });
    return { data: undefined, error: null };
  } catch (error: any) {
    console.error('Error updating project:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to update project' } };
  }
};

export const deleteProject = async (projectId: string): Promise<ServiceResult<void>> => {
  try {
    const projectRef = doc(db, COLLECTIONS.PROJECTS, projectId);
    await deleteDoc(projectRef);
    return { data: undefined, error: null };
  } catch (error: any) {
    console.error('Error deleting project:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to delete project' } };
  }
};

// Project Application operations
export const createProjectApplication = async (applicationData: Omit<ProjectApplication, 'id' | 'createdAt' | 'updatedAt'>): Promise<ServiceResult<string>> => {
  try {
    const applicationsCollection = collection(db, COLLECTIONS.PROJECTS, applicationData.projectId, 'applications');
    const docRef = await addDoc(applicationsCollection, {
      ...applicationData,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
    });
    return { data: docRef.id, error: null };
  } catch (error: any) {
    console.error('Error creating project application:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to create project application' } };
  }
};

export const getProjectApplications = async (projectId: string): Promise<ServiceResult<ProjectApplication[]>> => {
  try {
    const applicationsCollection = collection(db, COLLECTIONS.PROJECTS, projectId, 'applications');
    const applicationDocs = await getDocs(applicationsCollection);
    const applications = applicationDocs.docs.map(doc => ({ ...doc.data() as ProjectApplication, id: doc.id }));
    return { data: applications, error: null };
  } catch (error: any) {
    console.error('Error getting project applications:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to get project applications' } };
  }
};

export const updateProjectApplication = async (applicationId: string, updates: Partial<ProjectApplication>): Promise<ServiceResult<void>> => {
  try {
    const applicationRef = doc(db, COLLECTIONS.PROJECTS, updates.projectId as string, 'applications', applicationId);
    await updateDoc(applicationRef, {
      ...updates,
      updatedAt: Timestamp.now(),
    });
    return { data: undefined, error: null };
  } catch (error: any) {
    console.error('Error updating project application:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to update project application' } };
  }
};

// Connection operations
export const createConnectionRequest = async (userId: string, targetUserId: string): Promise<ServiceResult<string>> => {
  try {
    // Check if connection already exists or is pending
    const existingConnectionQuery = query(
      collection(db, COLLECTIONS.CONNECTIONS),
      where('userId', 'in', [userId, targetUserId]),
      where('connectedUserId', 'in', [userId, targetUserId])
    );
    const existingConnectionSnap = await getDocs(existingConnectionQuery);

    if (!existingConnectionSnap.empty) {
      return { data: null, error: { code: 'conflict', message: 'Connection already exists or is pending' } };
    }

    // Create connection request
    const connectionsCollection = collection(db, COLLECTIONS.CONNECTIONS);
    const docRef = await addDoc(connectionsCollection, {
      userId: userId,
      connectedUserId: targetUserId,
      status: 'pending',
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
    });
    return { data: docRef.id, error: null };
  } catch (error: any) {
    console.error('Error creating connection request:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to create connection request' } };
  }
};

export const getConnections = async (userId: string): Promise<ServiceResult<Connection[]>> => {
  try {
    const connectionsCollection = collection(db, COLLECTIONS.CONNECTIONS);
    const userConnectionsQuery = query(
      connectionsCollection,
      where('status', '==', 'accepted'),
      where('userId', '==', userId)
    );
    const connectedToUserQuery = query(
      connectionsCollection,
      where('status', '==', 'accepted'),
      where('connectedUserId', '==', userId)
    );

    const [userConnectionsSnap, connectedToUserSnap] = await Promise.all([
      getDocs(userConnectionsQuery),
      getDocs(connectedToUserQuery),
    ]);

    const connections = [
      ...userConnectionsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() as Connection })),
      ...connectedToUserSnap.docs.map(doc => ({ id: doc.id, ...doc.data() as Connection })),
    ];

    return { data: connections, error: null };
  } catch (error: any) {
    console.error('Error getting connections:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to get connections' } };
  }
};

export const updateConnectionStatus = async (connectionId: string, status: Connection['status']): Promise<ServiceResult<void>> => {
  try {
    const connectionRef = doc(db, COLLECTIONS.CONNECTIONS, connectionId);
    await updateDoc(connectionRef, {
      status,
      updatedAt: Timestamp.now(),
    });
    return { data: undefined, error: null };
  } catch (error: any) {
    console.error('Error updating connection status:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to update connection status' } };
  }
};

export const removeConnection = async (connectionId: string): Promise<ServiceResult<void>> => {
  try {
    const connectionRef = doc(db, COLLECTIONS.CONNECTIONS, connectionId);
    await deleteDoc(connectionRef);
    return { data: undefined, error: null };
  } catch (error: any) {
    console.error('Error removing connection:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to remove connection' } };
  }
};

// Review operations
export const createReview = async (reviewData: Omit<Review, 'id' | 'createdAt'>): Promise<ServiceResult<string>> => {
  try {
    const reviewsCollection = collection(db, COLLECTIONS.REVIEWS);
    const docRef = await addDoc(reviewsCollection, {
      ...reviewData,
      createdAt: Timestamp.now(),
    });
    return { data: docRef.id, error: null };
  } catch (error: any) {
    console.error('Error creating review:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to create review' } };
  }
};

export const getUserReviews = async (userId: string): Promise<ServiceResult<Review[]>> => {
  try {
    const reviewsCollection = collection(db, COLLECTIONS.REVIEWS);
    const userReviewsQuery = query(reviewsCollection, where('targetId', '==', userId), orderBy('createdAt', 'desc'));
    const reviewDocs = await getDocs(userReviewsQuery);
    const reviews = reviewDocs.docs.map(doc => ({ id: doc.id, ...doc.data() as Review }));
    return { data: reviews, error: null };
  } catch (error: any) {
    console.error('Error getting user reviews:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to get user reviews' } };
  }
};

// Challenge operations
export const getChallenge = async (challengeId: string): Promise<ServiceResult<Challenge | undefined>> => {
  try {
    const challengeDoc = await getDoc(doc(db, COLLECTIONS.CHALLENGES, challengeId));
    if (challengeDoc.exists()) {
      return { data: challengeDoc.data() as Challenge, error: null };
    } else {
      return { data: undefined, error: null };
    }
  } catch (error: any) {
    console.error('Error getting challenge:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to get challenge' } };
  }
};

export const getChallenges = async (): Promise<ServiceResult<Challenge[]>> => {
  try {
    const challengesCollection = collection(db, COLLECTIONS.CHALLENGES);
    const challengeDocs = await getDocs(challengesCollection);
    const challenges = challengeDocs.docs.map(doc => ({ id: doc.id, ...doc.data() as Challenge }));
    return { data: challenges, error: null };
  } catch (error: any) {
    console.error('Error getting challenges:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to get challenges' } };
  }
};

// Message operations
export const createMessage = async (conversationId: string, messageData: Omit<Message, 'id' | 'createdAt'>): Promise<ServiceResult<string>> => {
  try {
    const messagesCollection = collection(db, COLLECTIONS.CONVERSATIONS, conversationId, COLLECTIONS.MESSAGES);
    const docRef = await addDoc(messagesCollection, {
      ...messageData,
      createdAt: Timestamp.now(),
      read: false, // Messages are unread by default
    });

    // Update last message in conversation
    const conversationRef = doc(db, COLLECTIONS.CONVERSATIONS, conversationId);
    await updateDoc(conversationRef, {
      lastMessage: { id: docRef.id, senderId: messageData.senderId, content: messageData.content, createdAt: Timestamp.now(), read: false },
      lastMessageTimestamp: Timestamp.now(),
      updatedAt: Timestamp.now(),
      // Increment unread count for all participants except the sender
      // This requires fetching the conversation to know the participants
      // For now, a simpler approach: rely on frontend to mark as read
    });

    return { data: docRef.id, error: null };
  } catch (error: any) {
    console.error('Error creating message:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to create message' } };
  }
};

export const createConversation = async (participants: { id: string; name: string; }[], metadata: { tradeId?: string; tradeName?: string; conversationType?: string; }): Promise<ServiceResult<string>> => {
  try {
    const conversationsCollection = collection(db, COLLECTIONS.CONVERSATIONS);
    const docRef = await addDoc(conversationsCollection, {
      participants,
      ...metadata,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
      unreadCount: participants.reduce((acc, participant) => ({
        ...acc,
        [participant.id]: 0, // Initialize unread count to 0 for all participants
      }), {}),
    });
    return { data: docRef.id, error: null };
  } catch (error: any) {
    console.error('Error creating conversation:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to create conversation' } };
  }
};

export const getUserConversations = async (userId: string): Promise<ServiceResult<Conversation[]>> => {
  try {
    const conversationsCollection = collection(db, COLLECTIONS.CONVERSATIONS);
    const userConversationsQuery = query(
      conversationsCollection,
      where('participants', 'array-contains', { id: userId }),
      orderBy('updatedAt', 'desc')
    );
    const conversationDocs = await getDocs(userConversationsQuery);
    const conversations = conversationDocs.docs.map(doc => ({ id: doc.id, ...doc.data() as Conversation }));
    return { data: conversations, error: null };
  } catch (error: any) {
    console.error('Error getting user conversations:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to get user conversations' } };
  }
};

// Update user profile
// Collaboration operations
export const getCollaborations = async (): Promise<ServiceResult<Collaboration[]>> => {
  try {
    const collaborationsCollection = collection(db, COLLECTIONS.COLLABORATIONS);
    const collaborationDocs = await getDocs(collaborationsCollection);
    const collaborations = collaborationDocs.docs.map(doc => ({ id: doc.id, ...doc.data() as Collaboration }));
    return { data: collaborations, error: null };
  } catch (error: any) {
    console.error('Error getting collaborations:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to get collaborations' } };
  }
};

export const createCollaboration = async (collaborationData: Omit<Collaboration, 'id'>): Promise<ServiceResult<string>> => {
  try {
    const collaborationsCollection = collection(db, COLLECTIONS.COLLABORATIONS);
    const docRef = await addDoc(collaborationsCollection, {
      ...collaborationData,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
    });
    return { data: docRef.id, error: null };
  } catch (error: any) {
    console.error('Error creating collaboration:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to create collaboration' } };
  }
};

export const updateCollaboration = async (collaborationId: string, updates: Partial<Collaboration>): Promise<ServiceResult<void>> => {
  try {
    const collaborationRef = doc(db, COLLECTIONS.COLLABORATIONS, collaborationId);
    await updateDoc(collaborationRef, {
      ...updates,
      updatedAt: Timestamp.now(),
    });
    return { data: undefined, error: null };
  } catch (error: any) {
    console.error('Error updating collaboration:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to update collaboration' } };
  }
};

export const deleteCollaboration = async (collaborationId: string): Promise<ServiceResult<void>> => {
  try {
    const collaborationRef = doc(db, COLLECTIONS.COLLABORATIONS, collaborationId);
    await deleteDoc(collaborationRef);
    return { data: undefined, error: null };
  } catch (error: any) {
    console.error('Error deleting collaboration:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to delete collaboration' } };
  }
};

// System stats operations
export const getSystemStats = async (): Promise<ServiceResult<{
  totalUsers: number;
  totalTrades: number;
  totalProjects: number;
  totalCollaborations: number;
  activeUsers: number;
}>> => {
  try {
    // This is a simplified example. In a real application, 
    // you would likely use aggregate queries or cloud functions 
    // for more efficient stats calculation.
    const usersSnap = await getDocs(collection(db, COLLECTIONS.USERS));
    const tradesSnap = await getDocs(collection(db, COLLECTIONS.TRADES));
    const projectsSnap = await getDocs(collection(db, COLLECTIONS.PROJECTS));
    const collaborationsSnap = await getDocs(collection(db, COLLECTIONS.COLLABORATIONS));

    const totalUsers = usersSnap.size;
    const totalTrades = tradesSnap.size;
    const totalProjects = projectsSnap.size;
    const totalCollaborations = collaborationsSnap.size;
    const activeUsers = usersSnap.docs.filter(doc => {
      const lastSignInTime = doc.data().lastSignInTime;
      if (lastSignInTime) {
        const lastSignInDate = new Date(lastSignInTime);
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        return lastSignInDate > thirtyDaysAgo;
      }
      return false;
    }).length; // Simplified active user logic

    return { data: {
      totalUsers,
      totalTrades,
      totalProjects,
      totalCollaborations,
      activeUsers
    }, error: null };
  } catch (error: any) {
    console.error('Error getting system stats:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to get system stats' } };
  }
};

// Trade proposal operations
export const createTradeProposal = async (proposalData: Omit<TradeProposal, 'id' | 'createdAt' | 'updatedAt'>): Promise<ServiceResult<string>> => {
  try {
    const proposalsCollection = collection(db, COLLECTIONS.TRADES, proposalData.tradeId, 'proposals');
    const docRef = await addDoc(proposalsCollection, {
      ...proposalData,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
      status: 'pending', // Default status
    });
    return { data: docRef.id, error: null };
  } catch (error: any) {
    console.error('Error creating trade proposal:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to create trade proposal' } };
  }
};

export const getTradeProposals = async (tradeId: string): Promise<ServiceResult<TradeProposal[]>> => {
  try {
    const proposalsCollection = collection(db, COLLECTIONS.TRADES, tradeId, 'proposals');
    const proposalDocs = await getDocs(proposalsCollection);
    const proposals = proposalDocs.docs.map(doc => {
      const { id, ...proposalData } = doc.data() as TradeProposal;
      return {
        id: doc.id,
        ...proposalData,
      };
    });
    return { data: proposals, error: null };
  } catch (error: any) {
    console.error('Error getting trade proposals:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to get trade proposals' } };
  }
};

export const updateTradeProposalStatus = async (tradeId: string, proposalId: string, status: TradeProposal['status']): Promise<ServiceResult<void>> => {
  try {
    const proposalRef = doc(db, COLLECTIONS.TRADES, tradeId, 'proposals', proposalId);
    await updateDoc(proposalRef, {
      status,
      updatedAt: Timestamp.now(),
      ...(status === 'accepted' && { acceptedAt: Timestamp.now() }),
      ...(status === 'rejected' && { rejectedAt: Timestamp.now() }),
    });
    return { data: undefined, error: null };
  } catch (error: any) {
    console.error('Error updating trade proposal status:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to update trade proposal status' } };
  }
};

// Trade completion operations
export const requestTradeCompletion = async (
  tradeId: string,
  userId: string,
  notes?: string,
  evidence?: EmbeddedEvidence[]
): Promise<ServiceResult<void>> => {
  try {
    const tradeRef = doc(db, COLLECTIONS.TRADES, tradeId);
    await updateDoc(tradeRef, {
      status: 'pending_confirmation',
      completionRequestedBy: userId,
      completionRequestedAt: Timestamp.now(),
      completionNotes: notes,
      completionEvidence: evidence,
      updatedAt: Timestamp.now(),
    });
    return { data: undefined, error: null };
  } catch (error: any) {
    console.error('Error requesting trade completion:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to request trade completion' } };
  }
};

export const confirmTradeCompletion = async (
  tradeId: string,
  userId: string
): Promise<ServiceResult<void>> => {
  try {
    // Get trade data first to check participants and timing
    const tradeRef = doc(db, COLLECTIONS.TRADES, tradeId);
    const tradeSnap = await getDoc(tradeRef);

    if (!tradeSnap.exists()) {
      return { data: null, error: { code: 'not-found', message: 'Trade not found' } };
    }

    const trade = tradeSnap.data() as any;

    // Update trade status
    await updateDoc(tradeRef, {
      status: 'completed',
      completionConfirmedAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
    });

    // Award XP to both participants
    try {
      const { awardTradeCompletionXP } = await import('./gamification');

      // Check if confirmation was quick (within 24 hours)
      const isQuickResponse = trade.completionRequestedAt &&
        (Timestamp.now().seconds - trade.completionRequestedAt.seconds) < 86400;

      // Award XP to both users
      const participants = [trade.creatorId, trade.participantId].filter(Boolean);

      for (const participantId of participants) {
        if (participantId) {
          // Check if this is their first trade for bonus XP
          const userTradesQuery = query(
            collection(db, COLLECTIONS.TRADES),
            where('status', '==', 'completed'),
            where('creatorId', '==', participantId)
          );
          const userTradesSnap = await getDocs(userTradesQuery);
          const isFirstTrade = userTradesSnap.size === 0;

          await awardTradeCompletionXP(participantId, isQuickResponse, isFirstTrade);
        }
      }
    } catch (gamificationError: any) {
      // Log gamification error but don't fail the trade completion
      console.warn('Gamification XP award failed:', gamificationError.message);
    }

    return { data: undefined, error: null };
  } catch (error: any) {
    console.error('Error confirming trade completion:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to confirm trade completion' } };
  }
};

export const requestTradeChanges = async (
  tradeId: string,
  userId: string,
  reason: string
): Promise<ServiceResult<void>> => {
  try {
    const tradeRef = doc(db, COLLECTIONS.TRADES, tradeId);
    // Add a new change request to the array
    await updateDoc(tradeRef, {
      changeRequests: arrayUnion({
        id: doc(collection(db, '_')).id, // Generate a unique ID for the change request
        requestedBy: userId,
        requestedAt: Timestamp.now(),
        reason: reason,
        status: 'pending', // Default status for a new change request
        // resolvedAt is omitted on creation, as it is optional and only set when addressed/rejected
      }),
      updatedAt: Timestamp.now(),
    });
    // TODO: Notify the other party about the requested changes
    return { data: undefined, error: null };
  } catch (error: any) {
    console.error('Error requesting trade changes:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to request trade changes' } };
  }
};

export const submitTradeEvidence = async (
  tradeId: string,
  _userId: string,
  evidenceData: EvidenceUpdateData,
  isCreator: boolean
): Promise<ServiceResult<void>> => {
  try {
    const tradeRef = doc(db, COLLECTIONS.TRADES, tradeId);
    const updates: Partial<Trade> = {
      updatedAt: Timestamp.now(),
    };

    if (isCreator) {
      updates.creatorEvidence = evidenceData.evidence;
      updates.creatorCompletionNotes = evidenceData.notes;
      updates.creatorEvidenceSubmittedAt = Timestamp.now();
    } else {
      updates.participantEvidence = evidenceData.evidence;
      updates.participantCompletionNotes = evidenceData.notes;
      updates.participantEvidenceSubmittedAt = Timestamp.now();
    }

    await updateDoc(tradeRef, updates);

    // Check if both parties have submitted evidence and update status
    const tradeDoc = await getDoc(tradeRef);
    if (tradeDoc.exists()) {
      const trade = tradeDoc.data() as Trade;
      if (trade.creatorEvidence && trade.participantEvidence) {
        await updateDoc(tradeRef, { status: 'pending_confirmation', updatedAt: Timestamp.now() });
        // TODO: Notify creator that evidence has been submitted by participant
      }
    }

    return { data: undefined, error: null };
  } catch (error: any) {
    console.error('Error submitting trade evidence:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to submit trade evidence' } };
  }
};

export const getConnectionRequests = async (userId: string): Promise<ServiceResult<Connection[]>> => {
  try {
    const connectionsCollection = collection(db, COLLECTIONS.CONNECTIONS);
    const userRequestsQuery = query(
      connectionsCollection,
      where('status', '==', 'pending'),
      where('connectedUserId', '==', userId) // Requests where the current user is the recipient
    );
    const requestDocs = await getDocs(userRequestsQuery);
    const requests = requestDocs.docs.map(doc => ({ id: doc.id, ...doc.data() as Connection }));
    return { data: requests, error: null };
  } catch (error: any) {
    console.error('Error getting connection requests:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to get connection requests' } };
  }
};

export const getSentConnectionRequests = async (userId: string): Promise<ServiceResult<Connection[]>> => {
  try {
    const connectionsCollection = collection(db, COLLECTIONS.CONNECTIONS);
    const sentRequestsQuery = query(
      connectionsCollection,
      where('status', '==', 'pending'),
      where('userId', '==', userId) // Requests where the current user is the sender
    );
    const sentRequestDocs = await getDocs(sentRequestsQuery);
    const sentRequests = sentRequestDocs.docs.map(doc => ({ id: doc.id, ...doc.data() as Connection }));
    return { data: sentRequests, error: null };
  } catch (error: any) {
    console.error('Error getting sent connection requests:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to get sent connection requests' } };
  }
};

export const markMessageAsRead = async (): Promise<ServiceResult<void>> => {
  try {
    // This requires knowing the conversation ID, which is not available from just the message ID
    // A different data model or approach might be needed for efficient message read tracking per user
    // For now, this function is likely incomplete or needs a different signature.
    console.warn('markMessageAsRead not fully implemented for the current data model.');
    return { data: undefined, error: null }; // Or handle as an error
  } catch (error: any) {
    console.error('Error marking message as read:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to mark message as read' } };
  }
};

export const getPaginatedTrades = async (
  lastVisible?: QueryDocumentSnapshot<DocumentData>,
  pageSize: number = 10,
  category?: string
): Promise<ServiceResult<{
  trades: Trade[];
  lastVisible: QueryDocumentSnapshot<DocumentData> | undefined;
  hasMore: boolean;
}>> => {
  try {
    let tradesQuery = query(collection(db, COLLECTIONS.TRADES));
    if (category) {
      tradesQuery = query(tradesQuery, where('category', '==', category));
    }
    tradesQuery = query(tradesQuery, orderBy('createdAt', 'desc'));

    if (lastVisible) {
      tradesQuery = query(tradesQuery, startAfter(lastVisible));
    }

    tradesQuery = query(tradesQuery, limitQuery(pageSize + 1)); // Fetch one extra to check for more

    const tradeDocs = await getDocs(tradesQuery);
    const trades = tradeDocs.docs.map(doc => {
      const trade = { id: doc.id, ...doc.data() as Trade };

      // Add auto-completion countdown if pending confirmation
      if (trade.status === 'pending_confirmation' && trade.completionRequestedAt) {
        trade.autoCompletionCountdown = calculateAutoCompletionCountdown(trade.completionRequestedAt);
      }

      return trade;
    });

    const hasMore = trades.length > pageSize;
    const nextLastVisible = hasMore ? tradeDocs.docs[pageSize - 1] : undefined;

    return { data: {
      trades: trades.slice(0, pageSize),
      lastVisible: nextLastVisible,
      hasMore,
    }, error: null };

  } catch (error: any) {
    console.error('Error getting paginated trades:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to get paginated trades' } };
  }
};

export const updateUserProfile = async (
  uid: string,
  updates: UpdateUserProfileData
): Promise<ServiceResult<void>> => {
  try {
    const userRef = doc(db, COLLECTIONS.USERS, uid);
    await updateDoc(userRef, {
      ...updates,
      updatedAt: Timestamp.now(),
    });
    return { data: undefined, error: null };
  } catch (error: any) {
    console.error('Error updating user profile:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to update user profile' } };
  }
};

// Mark messages as read
export const markMessagesAsRead = async (conversationId: string, userId: string): Promise<ServiceResult<void>> => {
  try {
    const messagesRef = collection(db, COLLECTIONS.MESSAGES);
    const q = query(
      messagesRef,
      where('conversationId', '==', conversationId),
      where('senderId', '!=', userId)
    );
    const messagesSnap = await getDocs(q);

    const batch = writeBatch(db);
    messagesSnap.forEach((doc) => {
      batch.update(doc.ref, { read: true });
    });
    await batch.commit();

    return { data: undefined, error: null };
  } catch (error: any) {
    console.error('Error marking messages as read:', error);
    return { data: null, error: { code: error.code || 'unknown', message: error.message || 'Failed to mark messages as read' } };
  }
};

export type { ChangeRequest };

// Auto-completion helper functions
export const calculateAutoCompletionDate = (completionRequestedAt: Timestamp | Date): Date => {
  const requestDate = completionRequestedAt instanceof Timestamp
    ? completionRequestedAt.toDate()
    : completionRequestedAt;
  return new Date(requestDate.getTime() + 14 * 24 * 60 * 60 * 1000); // 14 days from request
};

export const calculateAutoCompletionCountdown = (completionRequestedAt: Timestamp | Date): number => {
  const autoCompleteDate = calculateAutoCompletionDate(completionRequestedAt);
  const now = new Date();
  const diffTime = autoCompleteDate.getTime() - now.getTime();
  return Math.max(0, Math.floor(diffTime / (1000 * 60 * 60 * 24))); // Days remaining
};

export const shouldSendReminder = (completionRequestedAt: Timestamp | Date, remindersSent: number = 0): boolean => {
  const requestDate = completionRequestedAt instanceof Timestamp
    ? completionRequestedAt.toDate()
    : completionRequestedAt;
  const now = new Date();
  const daysSinceRequest = Math.floor((now.getTime() - requestDate.getTime()) / (1000 * 60 * 60 * 24));

  // Send reminders at 3, 7, and 10 days
  if (daysSinceRequest >= 3 && remindersSent < 1) return true;
  if (daysSinceRequest >= 7 && remindersSent < 2) return true;
  if (daysSinceRequest >= 10 && remindersSent < 3) return true;

  return false;
};

export const shouldAutoComplete = (completionRequestedAt: Timestamp | Date): boolean => {
  const requestDate = completionRequestedAt instanceof Timestamp
    ? completionRequestedAt.toDate()
    : completionRequestedAt;
  const now = new Date();
  const daysSinceRequest = Math.floor((now.getTime() - requestDate.getTime()) / (1000 * 60 * 60 * 24));

  return daysSinceRequest >= 14;
};
