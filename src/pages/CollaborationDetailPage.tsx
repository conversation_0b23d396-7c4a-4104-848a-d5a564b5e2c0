import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { useAuth } from '../AuthContext';
import { useToast } from '../contexts/ToastContext';
import {
  CollaborationRoleData,
  RoleState
} from '../types/collaboration';
import { getCollaboration, Collaboration } from '../services/firestore';
import { getRoles } from '../services/collaborations';
import { CollaborationRolesSection } from '../components/collaboration/CollaborationRolesSection';
import { themeClasses } from '../utils/themeUtils';
import { motion } from 'framer-motion';
import ProfileImageWithUser from '../components/ui/ProfileImageWithUser';

interface CollaborationDetailProps {
  onError?: (error: Error) => void;
}

const CollaborationDetailPage: React.FC<CollaborationDetailProps> = ({ onError }) => {
  const { id } = useParams<{ id: string }>();
  const { currentUser } = useAuth();
  const { addToast } = useToast();
  const [collaboration, setCollaboration] = useState<Collaboration | null>(null);
  const [roles, setRoles] = useState<CollaborationRoleData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadCollaborationData = async () => {
    if (!id) return;

    try {
      setLoading(true);
      setError(null);

      // Load collaboration details
      const collaborationResult = await getCollaboration(id);
      if (collaborationResult.error) {
        throw new Error(collaborationResult.error.message);
      }

      if (!collaborationResult.data) {
        throw new Error('Collaboration not found');
      }

      setCollaboration(collaborationResult.data);

      // Load collaboration roles
      const rolesData = await getRoles(id);
      setRoles(rolesData);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load collaboration';
      setError(errorMessage);
      onError?.(err instanceof Error ? err : new Error(errorMessage));
      addToast('error', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadCollaborationData();
  }, [id]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-4 border-gray-200 dark:border-gray-700 border-t-orange-500 dark:border-t-orange-400"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-6 py-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">Error Loading Collaboration</h2>
          <p>{error}</p>
        </div>
      </div>
    );
  }

  if (!collaboration) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Collaboration not found</h2>
          <p className="mt-2 text-gray-600 dark:text-gray-400">The collaboration you're looking for doesn't exist or you don't have access to it.</p>
        </div>
      </div>
    );
  }

  // Check if current user is the creator
  const isCreator = currentUser?.uid === collaboration.creatorId;

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {/* Collaboration Header */}
        <div className={`${themeClasses.card} p-6 mb-8`}>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h1 className={`text-3xl font-bold ${themeClasses.heading1} mb-2`}>
                {collaboration.title}
              </h1>
              <p className={`text-lg ${themeClasses.textMuted} mb-4`}>
                {collaboration.description}
              </p>

              <div className="flex items-center space-x-6">
                <div className="flex items-center space-x-2">
                  <span className={`text-sm font-medium ${themeClasses.text}`}>Created by:</span>
                  <ProfileImageWithUser
                    userId={collaboration.creatorId}
                    profileUrl={collaboration.creatorPhotoURL}
                    size="small"
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <span className={`text-sm font-medium ${themeClasses.text}`}>Status:</span>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    collaboration.status === 'completed'
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                      : collaboration.status === 'in-progress'
                      ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
                      : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                  }`}>
                    {collaboration.status}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Collaboration Roles Section */}
        <div className="mb-8">
          <h2 className={`text-2xl font-bold ${themeClasses.heading2} mb-6`}>
            Project Roles
          </h2>
          <CollaborationRolesSection
            collaborationId={id!}
            collaborationTitle={collaboration.title}
            roles={roles}
            isCreator={isCreator}
            onRolesUpdated={loadCollaborationData}
          />
        </div>
      </motion.div>
    </div>
  );
};

export default CollaborationDetailPage;
