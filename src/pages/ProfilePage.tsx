import React, { useState, useEffect } from 'react';
import { useAuth } from '../AuthContext';
import { useParams } from 'react-router-dom';
import { getUserProfile } from '../services/firestore';
import { PortfolioTab } from '../components/features/portfolio/PortfolioTab';
import { GamificationDashboard } from '../components/gamification';
import { User, Trophy } from 'lucide-react';

type TabType = 'about' | 'portfolio' | 'gamification' | 'collaborations' | 'trades';

interface UserProfile {
  uid: string;
  email: string;
  displayName?: string;
  photoURL?: string;
  bio?: string;
  skills?: string[];
  location?: string;
  website?: string;
  metadata?: {
    creationTime?: string;
    lastSignInTime?: string;
  };
}

interface ProfilePageProps {
  userId?: string; // Optional prop for when used in modals
}

const ProfilePage: React.FC<ProfilePageProps> = ({ userId: propUserId }) => {
  const { currentUser } = useAuth();
  const { userId: paramUserId } = useParams<{ userId: string }>();
  const [activeTab, setActiveTab] = useState<TabType>('about');
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);

  // Use prop userId if provided, otherwise use URL param
  const userId = propUserId || paramUserId;

  // Determine if this is the user's own profile
  const isOwnProfile = !userId || userId === currentUser?.uid;
  const targetUserId = userId || currentUser?.uid;

  useEffect(() => {
    const loadUserProfile = async () => {
      setLoading(true);
      try {
        if (isOwnProfile && currentUser) {
          setUserProfile({
            uid: currentUser.uid,
            email: currentUser.email || '',
            displayName: currentUser.displayName || undefined,
            photoURL: currentUser.photoURL || undefined,
            metadata: {
              creationTime: currentUser.metadata.creationTime,
              lastSignInTime: currentUser.metadata.lastSignInTime
            }
          });
        } else if (targetUserId) {
          const { data: profile, error } = await getUserProfile(targetUserId);
          if (error) {
            console.error('Error loading user profile:', error);
          } else if (profile) {
            setUserProfile(profile as UserProfile);
          }
        }
      } catch (error) {
        console.error('Error loading user profile:', error);
      } finally {
        setLoading(false);
      }
    };

    loadUserProfile();
  }, [userId, currentUser, isOwnProfile, targetUserId]);

  const tabs: { id: TabType; label: string; icon?: React.ReactNode }[] = [
    { id: 'about', label: 'About', icon: <User className="w-4 h-4" /> },
    { id: 'portfolio', label: 'Portfolio' },
    { id: 'gamification', label: 'Progress', icon: <Trophy className="w-4 h-4" /> },
    { id: 'collaborations', label: 'Collaborations' },
    { id: 'trades', label: 'Trades' }
  ];

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="animate-pulse">
          <div className="bg-gray-200 h-8 w-48 mb-6 rounded"></div>
          <div className="bg-gray-200 h-64 rounded"></div>
        </div>
      </div>
    );
  }

  if (!userProfile) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12 text-center">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">User Not Found</h1>
        <p className="text-gray-600">The requested user profile could not be found.</p>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      {/* Profile Header */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
        <div className="p-6">
          <div className="flex items-center space-x-4">
            {userProfile.photoURL ? (
              <img
                src={userProfile.photoURL}
                alt={userProfile.displayName || 'User'}
                className="w-20 h-20 rounded-full object-cover"
              />
            ) : (
              <div className="w-20 h-20 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                <User className="w-8 h-8 text-gray-400" />
              </div>
            )}
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {userProfile.displayName || 'Anonymous User'}
              </h1>
              <p className="text-gray-600 dark:text-gray-400">{userProfile.email}</p>
              {userProfile.bio && (
                <p className="text-gray-700 dark:text-gray-300 mt-2">{userProfile.bio}</p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="-mb-px flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`${
                  activeTab === tab.id
                    ? 'border-orange-500 text-orange-600 dark:text-orange-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 transition-colors duration-200`}
              >
                {tab.icon}
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'about' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Email
                  </label>
                  <p className="px-3 py-2 bg-gray-50 dark:bg-gray-700 rounded text-gray-900 dark:text-gray-100">
                    {userProfile.email}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    User ID
                  </label>
                  <p className="px-3 py-2 bg-gray-50 dark:bg-gray-700 rounded text-gray-900 dark:text-gray-100 font-mono text-sm">
                    {userProfile.uid}
                  </p>
                </div>
                {userProfile.metadata?.creationTime && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Account Created
                    </label>
                    <p className="px-3 py-2 bg-gray-50 dark:bg-gray-700 rounded text-gray-900 dark:text-gray-100">
                      {new Date(userProfile.metadata.creationTime).toLocaleDateString()}
                    </p>
                  </div>
                )}
                {userProfile.metadata?.lastSignInTime && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Last Sign In
                    </label>
                    <p className="px-3 py-2 bg-gray-50 dark:bg-gray-700 rounded text-gray-900 dark:text-gray-100">
                      {new Date(userProfile.metadata.lastSignInTime).toLocaleDateString()}
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === 'portfolio' && targetUserId && (
            <PortfolioTab userId={targetUserId} isOwnProfile={isOwnProfile} />
          )}

          {activeTab === 'gamification' && targetUserId && (
            <GamificationDashboard userId={targetUserId} />
          )}

          {activeTab === 'collaborations' && (
            <div className="text-center py-12">
              <p className="text-gray-500 dark:text-gray-400">
                Collaborations feature coming soon
              </p>
            </div>
          )}

          {activeTab === 'trades' && (
            <div className="text-center py-12">
              <p className="text-gray-500 dark:text-gray-400">
                Trades feature coming soon
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;
