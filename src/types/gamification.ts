import { Timestamp } from 'firebase/firestore';

/**
 * User XP and Level System Types
 */
export interface UserXP {
  id?: string;
  userId: string;
  totalXP: number;
  currentLevel: number;
  xpToNextLevel: number;
  lastUpdated: Timestamp;
  createdAt: Timestamp;
}

export interface XPTransaction {
  id?: string;
  userId: string;
  amount: number;
  source: XPSource;
  sourceId?: string; // ID of the trade, role, etc. that triggered the XP
  description: string;
  multiplier?: number; // For bonus XP events
  createdAt: Timestamp;
}

export enum XPSource {
  TRADE_COMPLETION = 'trade_completion',
  ROLE_COMPLETION = 'role_completion',
  COLLABORATION_COMPLETION = 'collaboration_completion',
  PROFILE_COMPLETION = 'profile_completion',
  EVIDENCE_SUBMISSION = 'evidence_submission',
  QUICK_RESPONSE = 'quick_response',
  FIRST_TIME_BONUS = 'first_time_bonus',
  STREAK_BONUS = 'streak_bonus',
  ACHIEVEMENT_UNLOCK = 'achievement_unlock'
}

/**
 * Level System Configuration
 */
export interface LevelTier {
  level: number;
  title: string;
  minXP: number;
  maxXP: number;
  color: string;
  icon: string;
  benefits: string[];
}

export const LEVEL_TIERS: LevelTier[] = [
  {
    level: 1,
    title: 'Newcomer',
    minXP: 0,
    maxXP: 100,
    color: '#94a3b8',
    icon: '🌱',
    benefits: ['Basic platform access', 'Profile creation']
  },
  {
    level: 2,
    title: 'Explorer',
    minXP: 101,
    maxXP: 250,
    color: '#60a5fa',
    icon: '🔍',
    benefits: ['Trade creation', 'Basic collaboration access']
  },
  {
    level: 3,
    title: 'Contributor',
    minXP: 251,
    maxXP: 500,
    color: '#34d399',
    icon: '🤝',
    benefits: ['Enhanced profile features', 'Priority support']
  },
  {
    level: 4,
    title: 'Specialist',
    minXP: 501,
    maxXP: 1000,
    color: '#fbbf24',
    icon: '⭐',
    benefits: ['Advanced collaboration tools', 'Featured listings']
  },
  {
    level: 5,
    title: 'Expert',
    minXP: 1001,
    maxXP: 2000,
    color: '#f97316',
    icon: '🏆',
    benefits: ['Premium features', 'Mentorship opportunities']
  },
  {
    level: 6,
    title: 'Master',
    minXP: 2001,
    maxXP: 5000,
    color: '#8b5cf6',
    icon: '👑',
    benefits: ['Elite status', 'Custom features', 'Beta access']
  },
  {
    level: 7,
    title: 'Legend',
    minXP: 5001,
    maxXP: Infinity,
    color: '#ef4444',
    icon: '🔥',
    benefits: ['Legendary status', 'All features unlocked', 'Platform influence']
  }
];

/**
 * Achievement System Types
 */
export interface Achievement {
  id: string;
  title: string;
  description: string;
  category: AchievementCategory;
  rarity: AchievementRarity;
  icon: string;
  xpReward: number;
  unlockConditions: AchievementCondition[];
  isHidden?: boolean; // Hidden until unlocked
  createdAt: Timestamp;
}

export interface UserAchievement {
  id?: string;
  userId: string;
  achievementId: string;
  unlockedAt: Timestamp;
  progress?: number; // For progressive achievements
  isNotified?: boolean; // Whether user has been notified
}

export enum AchievementCategory {
  TRADING = 'trading',
  COLLABORATION = 'collaboration',
  COMMUNITY = 'community',
  SKILL = 'skill',
  MILESTONE = 'milestone',
  SPECIAL = 'special'
}

export enum AchievementRarity {
  COMMON = 'common',
  UNCOMMON = 'uncommon',
  RARE = 'rare',
  EPIC = 'epic',
  LEGENDARY = 'legendary'
}

export interface AchievementCondition {
  type: ConditionType;
  target: number;
  current?: number;
  metadata?: Record<string, any>;
}

export enum ConditionType {
  TRADE_COUNT = 'trade_count',
  ROLE_COUNT = 'role_count',
  XP_TOTAL = 'xp_total',
  STREAK_DAYS = 'streak_days',
  SKILL_LEVEL = 'skill_level',
  EVIDENCE_COUNT = 'evidence_count',
  QUICK_RESPONSES = 'quick_responses',
  COLLABORATION_RATING = 'collaboration_rating'
}

/**
 * Skill Development System Types
 */
export interface UserSkill {
  id?: string;
  userId: string;
  skillName: string;
  level: SkillLevel;
  xp: number;
  endorsements: number;
  lastUsed: Timestamp;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export enum SkillLevel {
  BEGINNER = 'beginner',
  INTERMEDIATE = 'intermediate',
  ADVANCED = 'advanced',
  EXPERT = 'expert'
}

export interface SkillEndorsement {
  id?: string;
  skillId: string;
  endorserId: string;
  endorserName: string;
  endorseeId: string;
  skillName: string;
  message?: string;
  createdAt: Timestamp;
}

/**
 * Gamification Service Response Types
 */
export interface XPAwardResult {
  success: boolean;
  xpAwarded: number;
  newLevel?: number;
  leveledUp: boolean;
  newAchievements?: Achievement[];
  error?: string;
}

export interface LevelCalculationResult {
  currentLevel: number;
  currentLevelTier: LevelTier;
  xpToNextLevel: number;
  progressPercentage: number;
}

/**
 * UI Component Props Types
 */
export interface XPDisplayProps {
  userId: string;
  showProgress?: boolean;
  showLevel?: boolean;
  compact?: boolean;
}

export interface AchievementBadgeProps {
  achievement: Achievement;
  unlocked: boolean;
  size?: 'small' | 'medium' | 'large';
  showTooltip?: boolean;
}

export interface LevelBadgeProps {
  level: number;
  size?: 'small' | 'medium' | 'large';
  showTitle?: boolean;
}

/**
 * Gamification Configuration
 */
export const XP_VALUES = {
  TRADE_COMPLETION: 100,
  ROLE_COMPLETION_BASE: 75,
  ROLE_COMPLETION_COMPLEX: 150,
  COLLABORATION_COMPLETION: 200,
  EVIDENCE_SUBMISSION: 25,
  QUICK_RESPONSE_BONUS: 50,
  FIRST_TRADE_BONUS: 100,
  FIRST_COLLABORATION_BONUS: 150,
  PROFILE_COMPLETION: 50,
  ACHIEVEMENT_UNLOCK: 25
} as const;

export const QUICK_RESPONSE_THRESHOLD = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
