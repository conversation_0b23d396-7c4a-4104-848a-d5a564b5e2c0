// Gamification Components Export
export { default as XPDisplay } from './XPDisplay';
export { default as LevelBadge, CompactLevelBadge, DetailedLevelBadge } from './LevelBadge';
export { default as AchievementBadge, CompactAchievementBadge, DetailedAchievementBadge } from './AchievementBadge';
export { default as GamificationDashboard } from './GamificationDashboard';

// Re-export types for convenience
export type {
  UserXP,
  XPTransaction,
  Achievement,
  UserAchievement,
  XPSource,
  LevelTier
} from '../../types/gamification';
