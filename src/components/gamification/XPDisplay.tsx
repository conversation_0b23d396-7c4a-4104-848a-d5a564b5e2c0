import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { getUserXP, calculateLevel } from '../../services/gamification';
import { UserXP, LevelCalculationResult, LEVEL_TIERS } from '../../types/gamification';
import { useAuth } from '../../AuthContext';

interface XPDisplayProps {
  userId?: string;
  showProgress?: boolean;
  showLevel?: boolean;
  compact?: boolean;
  className?: string;
}

export const XPDisplay: React.FC<XPDisplayProps> = ({
  userId,
  showProgress = true,
  showLevel = true,
  compact = false,
  className = ''
}) => {
  const { currentUser } = useAuth();
  const [userXP, setUserXP] = useState<UserXP | null>(null);
  const [levelInfo, setLevelInfo] = useState<LevelCalculationResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const targetUserId = userId || currentUser?.uid;

  useEffect(() => {
    const fetchUserXP = async () => {
      if (!targetUserId) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        const { success, data, error: fetchError } = await getUserXP(targetUserId);
        
        if (success && data) {
          setUserXP(data);
          const levelCalc = calculateLevel(data.totalXP);
          setLevelInfo(levelCalc);
        } else {
          setError(fetchError || 'Failed to load XP data');
        }
      } catch (err: any) {
        setError(err.message || 'Failed to load XP data');
      } finally {
        setLoading(false);
      }
    };

    fetchUserXP();
  }, [targetUserId]);

  if (loading) {
    return (
      <div className={`animate-pulse ${compact ? 'h-6' : 'h-12'} bg-gray-200 dark:bg-gray-700 rounded ${className}`} />
    );
  }

  if (error || !userXP || !levelInfo) {
    return (
      <div className={`text-gray-500 dark:text-gray-400 text-sm ${className}`}>
        {error || 'XP data unavailable'}
      </div>
    );
  }

  const progressPercentage = Math.min(100, levelInfo.progressPercentage);

  if (compact) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        {showLevel && (
          <div className="flex items-center space-x-1">
            <span className="text-lg">{levelInfo.currentLevelTier.icon}</span>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Lv.{levelInfo.currentLevel}
            </span>
          </div>
        )}
        <div className="text-sm text-gray-600 dark:text-gray-400">
          {userXP.totalXP.toLocaleString()} XP
        </div>
      </div>
    );
  }

  return (
    <motion.div
      className={`bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {showLevel && (
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <span className="text-2xl">{levelInfo.currentLevelTier.icon}</span>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Level {levelInfo.currentLevel}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {levelInfo.currentLevelTier.title}
              </p>
            </div>
          </div>
          <div className="text-right">
            <div className="text-lg font-bold text-gray-900 dark:text-white">
              {userXP.totalXP.toLocaleString()}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Total XP
            </div>
          </div>
        </div>
      )}

      {showProgress && (
        <div className="space-y-2">
          <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400">
            <span>Progress to Level {levelInfo.currentLevel + 1}</span>
            <span>
              {levelInfo.xpToNextLevel > 0 
                ? `${levelInfo.xpToNextLevel} XP to go`
                : 'Max Level!'
              }
            </span>
          </div>
          
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 overflow-hidden">
            <motion.div
              className="h-full rounded-full bg-gradient-to-r from-blue-500 to-purple-600"
              style={{ backgroundColor: levelInfo.currentLevelTier.color }}
              initial={{ width: 0 }}
              animate={{ width: `${progressPercentage}%` }}
              transition={{ duration: 1, ease: "easeOut" }}
            />
          </div>
          
          <div className="text-xs text-gray-500 dark:text-gray-500 text-center">
            {progressPercentage.toFixed(1)}% complete
          </div>
        </div>
      )}

      {/* Level Benefits */}
      {showLevel && levelInfo.currentLevelTier.benefits.length > 0 && (
        <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Level Benefits:
          </h4>
          <ul className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
            {levelInfo.currentLevelTier.benefits.map((benefit, index) => (
              <li key={index} className="flex items-center space-x-1">
                <span className="text-green-500">✓</span>
                <span>{benefit}</span>
              </li>
            ))}
          </ul>
        </div>
      )}
    </motion.div>
  );
};

export default XPDisplay;
