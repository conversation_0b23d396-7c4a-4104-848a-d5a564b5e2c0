import { useEffect, useRef } from 'react';
import { useAuth } from '../AuthContext';
import { processAutoResolution, shouldRunAutoResolution, markAutoResolutionRun } from '../services/autoResolution';

/**
 * Hook to automatically run auto-resolution when users visit the app
 * This replaces the need for Firebase Cloud Functions
 */
export const useAutoResolution = () => {
  const { currentUser } = useAuth();
  const hasRun = useRef(false);

  useEffect(() => {
    // Only run if user is authenticated and we haven't run yet in this session
    if (!currentUser || hasRun.current) {
      return;
    }

    // Check if we should run based on last run time
    if (!shouldRunAutoResolution()) {
      return;
    }

    const runAutoResolution = async () => {
      try {
        console.log('Running auto-resolution check...');
        const result = await processAutoResolution();
        
        if (result.remindersProcessed > 0 || result.tradesAutoCompleted > 0) {
          console.log('Auto-resolution completed:', {
            remindersProcessed: result.remindersProcessed,
            tradesAutoCompleted: result.tradesAutoCompleted
          });
        }

        if (result.errors.length > 0) {
          console.warn('Auto-resolution errors:', result.errors);
        }

        // Mark as run to prevent running again too soon
        markAutoResolutionRun();
        hasRun.current = true;

      } catch (error) {
        console.error('Auto-resolution failed:', error);
      }
    };

    // Run after a short delay to not block initial app loading
    const timeoutId = setTimeout(runAutoResolution, 2000);

    return () => clearTimeout(timeoutId);
  }, [currentUser]);
};

export default useAutoResolution;
