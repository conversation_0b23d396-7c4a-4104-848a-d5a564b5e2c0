/**
 * Main App Component
 *
 * Sets up routing and global providers for the application.
 */

import React, { Suspense, lazy } from 'react';
import { Navigate, Route, BrowserRouter, Routes, Link, useNavigate } from 'react-router-dom';
import { AuthProvider, useAuth } from './AuthContext';
import { NotificationsProvider } from './contexts/NotificationsContext';
import { ThemeProvider } from './contexts/ThemeContext';
import { ToastProvider, useToast } from './contexts/ToastContext';
import { MessageProvider } from './contexts/MessageContext';
import { RouteErrorBoundary } from './components/ui/ErrorBoundary';
import ErrorBoundaryWrapper from './components/ui/ErrorBoundaryWrapper';
import AdminRoute from './components/auth/AdminRoute';
import AppPreloader from './components/ui/AppPreloader';
import RoutePreloader from './components/ui/RoutePreloader';
import NetworkStatusIndicator from './components/ui/NetworkStatusIndicator';
import AutoResolutionProvider from './components/providers/AutoResolutionProvider';


// Lazy-loaded components
const HomePage = lazy(() => import('./pages/HomePage'));
const ProfileComponentsDemo = lazy(() => import('./pages/ProfileComponentsDemo'));
const CollaborationsPage = lazy(() => import('./pages/CollaborationsPage'));
const CreateProjectPage = lazy(() => import('./pages/CreateProjectPage'));
const CreateCollaborationPage = lazy(() => import('./pages/CreateCollaborationPage'));
const ProjectDetailPage = lazy(() => import('./pages/ProjectDetailPage'));
const CollaborationDetailPage = lazy(() => import('./pages/CollaborationDetailPage'));
const ConnectionsPage = lazy(() => import('./pages/ConnectionsPage'));
const UserDirectoryPage = lazy(() => import('./pages/UserDirectoryPage'));
const ChallengesPage = lazy(() => import('./pages/ChallengesPage'));
const ChallengeDetailPage = lazy(() => import('./pages/ChallengeDetailPage'));
const AdminDashboard = lazy(() => import('./pages/admin/AdminDashboard'));
const MessagesPage = lazy(() => import('./pages/MessagesPage').then(module => ({ default: module.MessagesPage })));
const BannerTestPage = lazy(() => import('./pages/BannerTestPage'));
const MigrationPage = lazy(() => import('./pages/MigrationPage'));

// Import ThemeToggle for NotificationsPage
import { ThemeToggle } from './components/ui/ThemeToggle';

const NotificationsPage = () => (
  <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <div className="flex justify-between items-center mb-6">
      <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Notifications</h1>
      <ThemeToggle />
    </div>
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 transition-colors duration-200">
      <p className="text-gray-600 dark:text-gray-300">Your notifications will appear here.</p>
    </div>
  </div>
);

// Import pages that we've created
import ProfilePage from './pages/ProfilePage';
import { TradesPage } from './pages/TradesPage';
import { TradeDetailPage } from './pages/TradeDetailPage';
import DashboardPage from './pages/DashboardPage';
import { SignUpPage } from './pages/SignUpPage';
import { PasswordResetPage } from './pages/PasswordResetPage';

// Import Evidence Test Page
import EvidenceTestPage from './pages/EvidenceTestPage';
// Production build - test pages removed

// Layout components
import Footer from './components/layout/Footer';
import Navbar from './components/layout/Navbar';

// Login page component
import EnhancedInput from './components/ui/EnhancedInput';
import { motion } from 'framer-motion';
import { Card } from './components/ui/Card';
import { MailIcon, LockIcon } from 'lucide-react';

const LoginPage = () => {
  const { signInWithEmail, signInWithGoogle, error, loading, currentUser } = useAuth();
  const { addToast } = useToast();
  const navigate = useNavigate();
  const [email, setEmail] = React.useState('');
  const [password, setPassword] = React.useState('');
  const [emailValid, setEmailValid] = React.useState<boolean | null>(null);
  const [passwordValid, setPasswordValid] = React.useState<boolean | null>(null);
  const [loginSuccess, setLoginSuccess] = React.useState(false);

  // If user is already logged in, redirect to dashboard
  React.useEffect(() => {
    if (currentUser && !loading) {
      navigate('/dashboard');
    }
  }, [currentUser, loading, navigate]);

  // Watch for successful login
  React.useEffect(() => {
    if (currentUser && loginSuccess) {
      // Show success toast
      addToast('success', 'Login successful! Welcome back.');

      // Navigate to dashboard
      navigate('/dashboard');
    }
  }, [currentUser, loginSuccess, navigate, addToast]);

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // Improved email change handling with validation feedback
  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setEmail(value);
    setEmailValid(value ? validateEmail(value) : null);
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setPassword(value);
    if (value) {
      setPasswordValid(value.length >= 6);
    } else {
      setPasswordValid(null);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await signInWithEmail(email, password);

    // Set login success flag to trigger the effect
    setLoginSuccess(true);
  };

  // Handle Google sign-in with success feedback
  const handleGoogleSignIn = async () => {
    await signInWithGoogle();

    // Set login success flag to trigger the effect
    setLoginSuccess(true);
  };

  return (
    <div className="max-w-md mx-auto px-4 sm:px-6 py-12">
      <Card variant="glass" className="p-8 shadow-lg">
        <motion.h1
          className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          Log In
        </motion.h1>

        {error && (
          <motion.div
            className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg mb-6"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            transition={{ duration: 0.3 }}
          >
            {String(error)}
          </motion.div>
        )}

        <motion.form
          onSubmit={handleSubmit}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="space-y-6"
        >
          <EnhancedInput
            id="email"
            type="email"
            label="Email"
            value={email}
            onChange={handleEmailChange}
            fullWidth
            required
            glassmorphism
            leftIcon={<MailIcon className="h-5 w-5" />}
            error={emailValid === false ? "Please enter a valid email address" : undefined}
            success={emailValid === true}
            placeholder="Enter your email"
            animateLabel
          />

          <div>
            <div className="flex justify-between items-center mb-1">
              <div className="h-5"></div> {/* Spacer to maintain layout with animated label */}
              <Link to="/reset-password" className="text-sm text-orange-500 hover:text-orange-700 transition-colors duration-200">
                Forgot password?
              </Link>
            </div>
            <EnhancedInput
              id="password"
              type="password"
              label="Password"
              value={password}
              onChange={handlePasswordChange}
              fullWidth
              required
              glassmorphism
              leftIcon={<LockIcon className="h-5 w-5" />}
              error={passwordValid === false ? "Password must be at least 6 characters" : undefined}
              success={passwordValid === true}
              placeholder="Enter your password"
              animateLabel
            />
          </div>

          <motion.button
            type="submit"
            className={`w-full px-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98] flex items-center justify-center ${
              loading
                ? 'bg-orange-400 cursor-not-allowed'
                : currentUser && loginSuccess
                  ? 'bg-green-500 hover:bg-green-600'
                  : 'bg-orange-500 hover:bg-orange-600'
            } text-white`}
            disabled={!!loading || !!(currentUser && loginSuccess)}
            whileHover={{ scale: !!loading || !!(currentUser && loginSuccess) ? 1 : 1.02 }}
            whileTap={{ scale: !!loading || !!(currentUser && loginSuccess) ? 1 : 0.98 }}
          >
            {loading ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Logging in...
              </>
            ) : currentUser && loginSuccess ? (
              <>
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                </svg>
                Success!
              </>
            ) : (
              'Log In'
            )}
          </motion.button>
        </motion.form>

        <div className="mt-6 relative">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-300 dark:border-gray-700"></div>
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400">
              Or continue with
            </span>
          </div>
        </div>

        <motion.button
          type="button"
          onClick={handleGoogleSignIn}
          className={`mt-4 w-full flex items-center justify-center gap-2 px-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98] ${
            loading
              ? 'bg-gray-100 dark:bg-gray-700 cursor-not-allowed text-gray-500 dark:text-gray-400 border border-gray-300 dark:border-gray-600'
              : currentUser && loginSuccess
                ? 'bg-white dark:bg-gray-800 text-green-600 dark:text-green-400 border border-green-300 dark:border-green-700'
                : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 border border-gray-300 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700'
          }`}
          disabled={!!loading || !!(currentUser && loginSuccess)}
          whileHover={{ scale: !!loading || !!(currentUser && loginSuccess) ? 1 : 1.02 }}
          whileTap={{ scale: !!loading || !!(currentUser && loginSuccess) ? 1 : 0.98 }}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          {loading ? (
            <>
              <svg className="animate-spin mr-2 h-5 w-5 text-gray-500 dark:text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span>Signing in...</span>
            </>
          ) : currentUser && loginSuccess ? (
            <>
              <svg className="w-5 h-5 mr-2 text-green-500 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
              </svg>
              <span>Signed in with Google</span>
            </>
          ) : (
            <>
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="24px" height="24px">
                <path fill="#FFC107" d="M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12c0-6.627,5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24c0,11.045,8.955,20,20,20c11.045,0,20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z" />
                <path fill="#FF3D00" d="M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z" />
                <path fill="#4CAF50" d="M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z" />
                <path fill="#1976D2" d="M43.611,20.083H42V20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z" />
              </svg>
              <span>Sign in with Google</span>
            </>
          )}
        </motion.button>

        <motion.div
          className="mt-6 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Don't have an account?{' '}
            <Link to="/signup" className="text-orange-500 hover:text-orange-700 font-medium transition-colors duration-200">
              Sign Up
            </Link>
          </p>
        </motion.div>
      </Card>
    </div>
  );
};

// Protected route component
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { currentUser, loading } = useAuth();

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-4 border-gray-200 border-t-orange-500"></div>
      </div>
    );
  }

  if (!currentUser) {
    return <Navigate to="/login" />;
  }

  return <>{children}</>;
};

const App: React.FC = () => {
  return (
    <BrowserRouter>
      <ErrorBoundaryWrapper>
        <ThemeProvider>
          <ToastProvider>
            <AuthProvider>
              <AutoResolutionProvider>
                <NotificationsProvider>
                  <MessageProvider>
                    <div className="min-h-screen flex flex-col bg-gray-50 dark:bg-gray-900">
                    {/* Preload critical application resources - TEMPORARILY DISABLED FOR TESTING */}
                    {/* <AppPreloader /> */}

                    {/* Preload route-specific resources - TEMPORARILY DISABLED FOR TESTING */}
                    {/* <RoutePreloader /> */}

                    <Navbar />
                    <main className="flex-grow">
                      <Suspense fallback={
                        <div className="flex items-center justify-center min-h-screen">
                          <div className="animate-spin rounded-full h-12 w-12 border-4 border-gray-200 dark:border-gray-700 border-t-orange-500 dark:border-t-orange-400"></div>
                        </div>
                      }>
                        <Routes>
                        <Route path="/" element={<RouteErrorBoundary><HomePage /></RouteErrorBoundary>} />
                        <Route path="/trades" element={<RouteErrorBoundary><TradesPage /></RouteErrorBoundary>} />
                      <Route path="/trades/:tradeId" element={<RouteErrorBoundary><TradeDetailPage /></RouteErrorBoundary>} />
                      <Route path="/projects" element={<RouteErrorBoundary><CollaborationsPage /></RouteErrorBoundary>} />
                      <Route path="/projects/new" element={<RouteErrorBoundary><CreateProjectPage /></RouteErrorBoundary>} />
                      <Route path="/projects/:projectId" element={<RouteErrorBoundary><ProjectDetailPage /></RouteErrorBoundary>} />
                      <Route path="/collaborations/new" element={<RouteErrorBoundary><CreateCollaborationPage /></RouteErrorBoundary>} />
                      <Route path="/collaborations/:id" element={<RouteErrorBoundary><CollaborationDetailPage /></RouteErrorBoundary>} />
                      <Route path="/connections" element={<RouteErrorBoundary><ConnectionsPage /></RouteErrorBoundary>} />
                      <Route path="/directory" element={<RouteErrorBoundary><UserDirectoryPage /></RouteErrorBoundary>} />
                      <Route path="/challenges" element={<RouteErrorBoundary><ChallengesPage /></RouteErrorBoundary>} />
                      <Route path="/challenges/:challengeId" element={<RouteErrorBoundary><ChallengeDetailPage /></RouteErrorBoundary>} />
                      <Route path="/login" element={<RouteErrorBoundary><LoginPage /></RouteErrorBoundary>} />
                      <Route path="/signup" element={<RouteErrorBoundary><SignUpPage /></RouteErrorBoundary>} />
                      <Route path="/profile-components" element={<RouteErrorBoundary><ProfileComponentsDemo /></RouteErrorBoundary>} />
                      <Route path="/admin" element={<RouteErrorBoundary><AdminRoute><AdminDashboard /></AdminRoute></RouteErrorBoundary>} />
                      <Route path="/reset-password" element={<RouteErrorBoundary><PasswordResetPage /></RouteErrorBoundary>} />

                      {/* Protected routes */}
                      <Route
                        path="/dashboard"
                        element={
                          <RouteErrorBoundary>
                            <ProtectedRoute>
                              <DashboardPage />
                            </ProtectedRoute>
                          </RouteErrorBoundary>
                        }
                      />
                      <Route
                        path="/profile"
                        element={
                          <RouteErrorBoundary>
                            <ProtectedRoute>
                              <ProfilePage />
                            </ProtectedRoute>
                          </RouteErrorBoundary>
                        }
                      />

                      <Route path="/profile/:userId" element={<RouteErrorBoundary><ProfilePage /></RouteErrorBoundary>} />

                      <Route path="/messages" element={<RouteErrorBoundary><MessagesPage /></RouteErrorBoundary>} />
                      <Route path="/messages/:conversationId" element={<RouteErrorBoundary><MessagesPage /></RouteErrorBoundary>} />

                      <Route path="/notifications" element={<RouteErrorBoundary><NotificationsPage /></RouteErrorBoundary>} />

                      {/* Test routes */}
                      <Route path="/banner-test" element={<RouteErrorBoundary><BannerTestPage /></RouteErrorBoundary>} />
                      <Route path="/evidence-demo" element={<RouteErrorBoundary><EvidenceTestPage /></RouteErrorBoundary>} />

                      {/* Admin routes */}
                      <Route path="/admin/migrations" element={<RouteErrorBoundary><AdminRoute><MigrationPage /></AdminRoute></RouteErrorBoundary>} />

                      {/* 404 route */}
                      <Route
                        path="*"
                        element={
                          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 text-center">
                            <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">404</h1>
                            <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">Page not found</p>
                            <Link
                              to="/"
                              className="inline-block bg-orange-500 text-white px-4 py-2 rounded-md hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors duration-200"
                            >
                              Go Home
                            </Link>
                          </div>
                        }
                      />
                    </Routes>
                    </Suspense>
                  </main>
                  <Footer />

                      {/* Network status indicator */}
                      <NetworkStatusIndicator />
                    </div>
                  </MessageProvider>
                </NotificationsProvider>
              </AutoResolutionProvider>
            </AuthProvider>
          </ToastProvider>
        </ThemeProvider>
      </ErrorBoundaryWrapper>
    </BrowserRouter>
  );
};

export default App;
